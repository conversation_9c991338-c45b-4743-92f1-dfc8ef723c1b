const storyTextElement = document.getElementById('story-text');
const choicesContainerElement = document.getElementById('choices-container');
const storyImageElement = document.getElementById('story-image');
const backgroundAudio = document.getElementById('background-audio');
const countdownElement = document.createElement('div');
countdownElement.id = 'countdown';

let countdownInterval;

let audioStarted = false;

document.body.addEventListener('click', () => {
    if (!audioStarted) {
        backgroundAudio.play();
        audioStarted = true;
    }
}, { once: true });


// Game state can be expanded later, for example, to track inventory
let playerState = {
    hasCloak: false,
};

const gameData = {
    'START': {
        text: '',
        image: 'assets/key_nobg.png',
        choices: [
            { text: 'Fioletowy', nextNode: 'VIOLET_PATH' },
            { text: 'Zielony', nextNode: 'GREEN_PATH' }
        ]
    },
    'GREEN_PATH': {
        text: 'C<PERSON><PERSON>sz za sobą obecność czegoś nie z tego świata...',
        choices: [
            { text: 'Uciekam', nextNode: 'RUN_AWAY_GREEN' },
            { text: 'Odwracam się', nextNode: 'TURN_AROUND_GREEN' }
        ]
    },
    'RUN_AWAY_GREEN': {
        text: 'To, co Cię goniło okazało się szybsze. Stwór szybkim kopnięciem podciął Ci nogi, upadasz na ziemie. To ostatnie co widzisz to miecz opadający na twoją głowę...',
        image: 'assets/green.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'TURN_AROUND_GREEN': {
        text: 'Twoim oczom ukazuje się monstrum, cuchnące rozkładem i siarką. Ostatnie co widzisz, to miecz opadający na twoją głowę...',
        image: 'assets/green.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'VIOLET_PATH': {
        text: 'Widzisz przed sobą ciemny korytarz, spowity mgłą.',
        choices: [
            { text: 'Wchodzę we mglę', nextNode: 'ENTER_MIST' },
            { text: 'Szukam innej drogi', nextNode: 'LOOK_DIFFERENT_WAY' }
        ]
    },
    'LOOK_DIFFERENT_WAY': {
        text: 'Znajdujesz wejście do pokoju, wolnego od mgły. Masz przed sobą trzy kolejne przejścia...',
        choices: [
            { text: 'Lewo', nextNode: 'GO_LEFT' },
            { text: 'Środek', nextNode: 'GO_MIDDLE' },
            { text: 'Prawo', nextNode: 'GO_RIGHT' }
        ]
    },
    'GO_MIDDLE': {
        text: 'Za drzwiami twoim oczom ukazała się sterta szmat, cuchnących stęchlizną i żelazem...',
        choices: [
            { text: 'Wracam się', nextNode: 'LOOK_DIFFERENT_WAY' }
        ]
    },
    'GO_LEFT': {
        text: 'Za drzwiami znajdowała się ogromna sala, z piedestałem na samym środku. Po bliższych oględzinach, znajdujesz na nim pelerynę niewidkę.',
        choices: [
            { text: 'Zabieram pelerynę i wracam się do korytarza z mgłą', nextNode: 'SNEAK_PAST_FIGURE', action: 'takeCloak' }
        ]
    },
    'GO_RIGHT': {
        text: 'Za ciężkimi, dębowymi drzwiami znajdował się skarbiec, pełen złotych monet, klejnotów, artefaktów.',
        choices: [
            { text: 'Biorę kilka monet', nextNode: 'RUCKUS_FEW_COINS' },
            { text: 'Zabieram tyle ile mogę', nextNode: 'RUCKUS_TAKE_ALL' }
        ]
    },
    'RUCKUS_FEW_COINS': {
        text: 'Słyszysz jakiś ruch z drugiego końca skarbca...',
        choices: [
            { text: 'Uciekam', nextNode: 'RUN_AWAY_TREASURY' },
            { text: 'Ukrywam się', nextNode: 'HIDE_IN_TREASURY' }
        ]
    },
    'RUN_AWAY_TREASURY': {
        text: 'Potykasz się o puchar leżący na ziemi. Stajesz się kolejnym elementem kolekcji.',
        image: 'assets/treasure.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'HIDE_IN_TREASURY': {
        text: 'Ukrywasz się pomiędzy stertami monet. Cokolwiek to było, nie znalazło cię. Rzucasz się do ucieczki, lecz tuż przy samych drzwiach stoi On...',
        image: 'assets/treasure.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'RUCKUS_TAKE_ALL': {
        text: 'Słyszysz jakiś ruch z drugiego końca skarbca. Próbujesz uciec, ale twoje kieszenie są przepełnione ciężkim złotem.',
        image: 'assets/treasure.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'ENTER_MIST': {
        text: 'W mgle widzisz dziwną postać...',
        choices: [
            { text: 'Podchodzę bliżej', nextNode: 'APPROACH_FIGURE' },
            { text: 'Przyglądam się', nextNode: 'LOOK_CLOSER' }
        ]
    },
    'APPROACH_FIGURE': {
        text: 'Zamaskowana postać wyczuwa twoją obecność. Czujesz nóż w trzewiach, a następnie paraliż trucizny...',
        image: 'assets/knife.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'LOOK_CLOSER': {
        text: 'Ma na sobie drugą pelerynę, a twarz zakrywa czaszka zwierzęcia. Z każdym ruchem, kręgosłup zwisający z czaszki wydaje przeraźliwe skrzypnięcia...',
        image: 'assets/silent.jpg',
        choices: [
            { text: 'Próbuje się przekraść obok', nextNode: 'APPROACH_FIGURE' },
            { text: 'Wracam się', nextNode: 'VIOLET_PATH' }
        ]
    },
    'SNEAK_PAST_FIGURE': {
        text: 'Przechodzisz niepostrzeżenie obok postaci dzięki pelerynie niewidce, ale nagle słyszysz za sobą dźwięk stóp na betonie...',
        choices: [
            { text: 'Wbiegam do pokoju', nextNode: 'RUN_INTO_ROOM' },
            { text: 'Wbiegam w kolejny korytarz', nextNode: 'RUN_INTO_CORRIDOR' },
            { text: 'Biegnę przed siebie', nextNode: 'RUN_STRAIGHT_AHEAD' }
        ]
    },
    'RUN_INTO_ROOM': {
        text: 'W pokoju widzisz starszą osobę opartą o ścianę. Wygląda bardzo podobnie do Ciebie...',
        choices: [
            { text: 'Biegnę dalej', nextNode: 'LEAVE_PERSON' },
            { text: 'Próbuję pomóc', nextNode: 'HELP_PERSON' }
        ]
    },
    'LEAVE_PERSON': {
        text: 'Pozostawiasz tą osobę na pastwę potwora...',
        choices: [
            { text: 'Biegnę dalej', nextNode: 'RUN_STRAIGHT_AHEAD' }
        ]
    },
    'RUN_INTO_CORRIDOR': {
        text: 'Ślepy zaułek',
        choices: [
            { text: 'Ukrywam się we wnęce', nextNode: 'APPROACH_FIGURE' }
        ]
    },
    'RUN_STRAIGHT_AHEAD': {
        text: 'Udało ci się uciec, ale czujesz jak grunt pod twoimi nogami się rusza. Widzisz przed sobą dwa korytarze...',
        choices: [
            { text: 'Jasny korytarz, tuż za górą gruzu', nextNode: 'BRIGHT_CORRIDOR' },
            { text: 'Ciemny korytarz, z którego dochodzi jęk', nextNode: 'DARK_CORRIDOR' }
        ]
    },
    'HELP_PERSON': {
        text: 'Osoba łapie Cię za ramię i nie chce wypuścić. Widzisz cień zbliżającego się potwora...',
        image: 'assets/knife.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'BRIGHT_CORRIDOR': {
        text: 'Góra gruzu okazała się miejscem spoczynku śniącego generała. Czujesz jak twój umysł otacza sen, a nogi korzenie.',
        image: 'assets/general.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'DARK_CORRIDOR': {
        text: 'Na końcu korytarza widzisz strażnika. Gdy się zbliżasz, zadaje Ci proste pytanie: "Czy uważasz się za dobrego człowieka?"',
        image: 'assets/guardian1.jpg',
        choices: [
            { text: 'Tak', nextNode: 'ANSWER_YES' },
            { text: 'Nie', nextNode: 'ANSWER_NO' }
        ]
    },
    'ANSWER_YES': {
        text: '“Jakbyś nim był*, to by Cię tu nie było”. Zaczynasz czuć się dziwnie, spoglądasz na swoje ręce, widzisz jak się nagle starzejesz. Strażnik zaczyna cię prowadzić do innego pokoju...',
        image: 'assets/guardian2.jpg',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'ANSWER_NO': {
        text: 'Strażnik schodzi Ci z drogi. Na odchodne rzuca "Musisz coś poświęcić, aby się stąd wydostać..."',
        choices: [
            { text: 'Tożsamość', nextNode: 'LOSE_IDENTITY' },
            { text: 'Dusza', nextNode: 'LOSE_SOUL' },
            { text: 'Pamięć', nextNode: 'LOSE_MEMORY' },
            { text: 'Umysł', nextNode: 'LOSE_MIND' }
        ]
    },
    'LOSE_IDENTITY': {
        text: 'Czujesz, jak tracisz swoją twarz. Nie jesteś w stanie znaleźć wyjścia, ani samego/ej siebie...',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'LOSE_SOUL': {
        text: 'Tracisz wszelką motywację. Siadasz na ziemi i czekasz...',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'LOSE_MEMORY': {
        text: 'Zapominasz czemu tutaj jesteś. Wracasz się we mgłę...',
        choices: [
            { text: 'Koniec', nextNode: 'START' }
        ]
    },
    'LOSE_MIND': {
        text: 'Gratulacje ukończenia rekrutacji! Ktoś będzie się z tobą kontaktował.',
        image: 'assets/logo.png',
        hasTimer: true,
        targetDate: '2025-08-12T20:00:00+02:00', // August 12, 2025, 8:00 PM in Polish time (CEST/GMT+2)
        choices: []
    }
};

function startGame() {
    playerState = { hasCloak: false };
    showNode('START');
}

function showNode(nodeName) {
    const node = gameData[nodeName];
    storyTextElement.innerText = node.text;
    choicesContainerElement.innerHTML = '';

    // Clear any existing countdown
    clearInterval(countdownInterval);
    countdownElement.remove(); // Remove countdown element if it exists

    if (node.image) {
        storyImageElement.src = node.image;
        storyImageElement.style.display = 'block';
    } else {
        storyImageElement.style.display = 'none';
    }

    if (node.hasTimer) {
        // Display countdown timer
        choicesContainerElement.appendChild(countdownElement);
        startCountdown(node.targetDate);
        return;
    }

    if (!node.choices || node.choices.length === 0) {
        const button = document.createElement('button');
        button.innerText = 'Powrót na start';
        button.addEventListener('click', () => startGame());
        choicesContainerElement.appendChild(button);
        return;
    }

    node.choices.forEach(choice => {
        const button = document.createElement('button');
        button.innerText = choice.text;
        button.addEventListener('click', () => selectChoice(choice));
        choicesContainerElement.appendChild(button);
    });
}

function selectChoice(choice) {
    // Handle actions that change player state
    if (choice.action === 'takeCloak') {
        playerState.hasCloak = true;
    }

    if (choice.nextNode) {
        showNode(choice.nextNode);
    } else {
        // If no nextNode, treat as an end state
        const button = document.createElement('button');
        button.innerText = 'Powrót na start';
        button.addEventListener('click', () => startGame());
        choicesContainerElement.innerHTML = '';
        choicesContainerElement.appendChild(button);
    }
}

function startCountdown(targetDateString) {
    const targetDate = new Date(targetDateString).getTime();

    countdownInterval = setInterval(() => {
        const now = new Date().getTime();
        const distance = targetDate - now;

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        countdownElement.innerHTML = `${days}d ${hours}g ${minutes}m ${seconds}s`;

        if (distance < 0) {
            clearInterval(countdownInterval);
            countdownElement.innerHTML = "REKRUTACJA ZAKOŃCZONA!";
        }
    }, 1000);
}

startGame();