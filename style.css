body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
}

h1 {
    position: absolute;
    top: 10px;
    left: 10px;
    margin: 0;
    font-size: 1.5em;
    animation: glitch 0.25s infinite;
}

@keyframes glitch {
    0% {
        transform: translate(0);
        text-shadow: none;
    }
    25% {
        transform: translate(-1px, 1px);
        text-shadow: -1px 0 red, 1px 0 blue;
    }
    50% {
        transform: translate(1px, -1px);
        text-shadow: 1px 0 red, -1px 0 blue;
    }
    75% {
        transform: translate(-1px, -1px);
        text-shadow: -1px 0 red, 1px 0 blue;
    }
    100% {
        transform: translate(0);
        text-shadow: none;
    }
}

#music-toggle {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #e0e0e0;
    font-size: 0.9em;
}

#music-toggle input[type="checkbox"] {
    margin-right: 5px;
}

#game-container {
    width: 80%;
    max-width: 800px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#image-container {
    margin-bottom: 20px;
    display: flex;
    align-items: center; /* Centers vertically */
    justify-content: center; /* Centers horizontally if needed */
    min-height: 200px; /* Give it some height to demonstrate vertical centering */
}

#story-image {
    max-width: 100%;
    max-height: 400px;
    display: none; /* Hidden by default */
    filter: grayscale(100%); /* Black and white filter */
}

#story-text {
    margin-bottom: 20px;
    font-size: 1.2em;
}

#choices-container button {
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    padding: 10px 20px;
    margin: 5px;
    cursor: pointer;
    font-size: 1em;
    border-radius: 5px;
}

#choices-container button:hover {
    background-color: #555;
}

#countdown {
    margin-top: 20px;
    font-size: 1.5em;
    font-weight: bold;
    color: #FFFFFF; /* White */
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.7); /* Subtle white glow */
}